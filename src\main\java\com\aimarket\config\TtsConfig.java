package com.aimarket.config;

import com.aimarket.core.tts.TtsFactory;
import com.aimarket.core.tts.provider.MinimaxTtsProvider;
import com.aimarket.core.tts.provider.MoyinTtsProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

import jakarta.annotation.PostConstruct;

/**
 * TTS Configuration
 * 
 * Configures TTS providers and registers them with the factory.
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class TtsConfig {

    private final TtsFactory ttsFactory;
    private final MinimaxTtsProvider minimaxTtsProvider;
    private final MoyinTtsProvider moyinTtsProvider;

    @Bean
    public WebClient webClient() {
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
    }

    @PostConstruct
    public void registerProviders() {
        log.info("Registering TTS providers...");
        
        // Register Minimax provider
        ttsFactory.registerProvider("minimax", minimaxTtsProvider);
        log.info("Minimax TTS provider registered");
        
        // Register Moyin provider
        ttsFactory.registerProvider("moyin", moyinTtsProvider);
        log.info("Moyin TTS provider registered");
        
        log.info("TTS providers registration completed. Available providers: {}", 
                ttsFactory.getAvailableProviders());
    }
}
