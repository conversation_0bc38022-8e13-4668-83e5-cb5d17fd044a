package com.aimarket.core.tts;

import com.aimarket.core.tts.dto.TtsRequest;
import com.aimarket.core.tts.dto.TtsResponse;

import java.util.List;

/**
 * TTS Provider Interface
 * 
 * Defines the contract for all TTS service providers.
 */
public interface TtsProvider {

    /**
     * Get provider name
     */
    String getProviderName();

    /**
     * Check if provider is available
     */
    boolean isAvailable();

    /**
     * Synthesize text to speech
     */
    TtsResponse synthesize(TtsRequest request);

    /**
     * Get available voices
     */
    List<Voice> getAvailableVoices();

    /**
     * Get supported languages
     */
    List<String> getSupportedLanguages();

    /**
     * Get supported formats
     */
    List<String> getSupportedFormats();

    /**
     * Voice information
     */
    record Voice(
            String id,
            String name,
            String language,
            String gender,
            String description,
            boolean isDefault
    ) {}
}
