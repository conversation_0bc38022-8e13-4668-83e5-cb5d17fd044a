package com.aimarket.core.tts;

import com.aimarket.config.AiMarketProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * TTS Factory
 * 
 * Factory class for managing TTS providers using factory pattern.
 * Supports load balancing and provider selection.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TtsFactory {

    private final Map<String, TtsProvider> providers = new ConcurrentHashMap<>();
    private final Map<String, AtomicInteger> roundRobinCounters = new ConcurrentHashMap<>();
    private final AiMarketProperties aiMarketProperties;

    /**
     * Register a TTS provider
     */
    public void registerProvider(String name, TtsProvider provider) {
        providers.put(name, provider);
        roundRobinCounters.put(name, new AtomicInteger(0));
        log.info("TTS provider registered: {}", name);
    }

    /**
     * Get TTS provider by name
     */
    public TtsProvider getProvider(String providerName) {
        if (providerName == null || providerName.isEmpty()) {
            providerName = aiMarketProperties.getTts().getDefaultProvider();
        }

        TtsProvider provider = providers.get(providerName);
        if (provider == null) {
            throw new RuntimeException("TTS provider not found: " + providerName);
        }

        if (!provider.isAvailable()) {
            throw new RuntimeException("TTS provider not available: " + providerName);
        }

        return provider;
    }

    /**
     * Get default provider
     */
    public TtsProvider getDefaultProvider() {
        return getProvider(aiMarketProperties.getTts().getDefaultProvider());
    }

    /**
     * Get provider with load balancing
     */
    public TtsProvider getProviderWithLoadBalancing() {
        String loadBalanceStrategy = aiMarketProperties.getTts().getLoadBalance();
        
        switch (loadBalanceStrategy.toLowerCase()) {
            case "round-robin":
                return getProviderRoundRobin();
            case "random":
                return getProviderRandom();
            case "least-used":
                return getProviderLeastUsed();
            default:
                return getDefaultProvider();
        }
    }

    /**
     * Get provider using round-robin strategy
     */
    private TtsProvider getProviderRoundRobin() {
        List<String> availableProviders = getAvailableProviders();
        if (availableProviders.isEmpty()) {
            throw new RuntimeException("No available TTS providers");
        }

        String defaultProvider = aiMarketProperties.getTts().getDefaultProvider();
        AtomicInteger counter = roundRobinCounters.get(defaultProvider);
        if (counter == null) {
            counter = new AtomicInteger(0);
            roundRobinCounters.put(defaultProvider, counter);
        }

        int index = counter.getAndIncrement() % availableProviders.size();
        String selectedProvider = availableProviders.get(index);
        
        return getProvider(selectedProvider);
    }

    /**
     * Get provider using random strategy
     */
    private TtsProvider getProviderRandom() {
        List<String> availableProviders = getAvailableProviders();
        if (availableProviders.isEmpty()) {
            throw new RuntimeException("No available TTS providers");
        }

        int randomIndex = (int) (Math.random() * availableProviders.size());
        String selectedProvider = availableProviders.get(randomIndex);
        
        return getProvider(selectedProvider);
    }

    /**
     * Get provider using least-used strategy
     */
    private TtsProvider getProviderLeastUsed() {
        // This would require tracking usage statistics
        // For now, fallback to default provider
        return getDefaultProvider();
    }

    /**
     * Get list of available providers
     */
    public List<String> getAvailableProviders() {
        return providers.entrySet().stream()
                .filter(entry -> entry.getValue().isAvailable())
                .map(Map.Entry::getKey)
                .toList();
    }

    /**
     * Get all registered providers
     */
    public Map<String, TtsProvider> getAllProviders() {
        return Map.copyOf(providers);
    }

    /**
     * Check if provider is registered
     */
    public boolean isProviderRegistered(String providerName) {
        return providers.containsKey(providerName);
    }

    /**
     * Remove provider
     */
    public void removeProvider(String providerName) {
        providers.remove(providerName);
        roundRobinCounters.remove(providerName);
        log.info("TTS provider removed: {}", providerName);
    }

    /**
     * Get provider health status
     */
    public Map<String, Boolean> getProviderHealthStatus() {
        return providers.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().isAvailable()
                ));
    }
}
