package com.aimarket.core.tts;

import com.aimarket.config.AiMarketProperties;
import com.aimarket.core.tts.dto.TtsRequest;
import com.aimarket.core.tts.dto.TtsResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Abstract TTS Provider
 * 
 * Base implementation for TTS providers with common functionality.
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractTtsProvider implements TtsProvider {

    protected final WebClient webClient;
    protected final AiMarketProperties.TtsConfig.ProviderConfig config;
    private final AtomicInteger apiKeyIndex = new AtomicInteger(0);

    @Override
    public boolean isAvailable() {
        return config.isEnabled() && 
               config.getApiKeys() != null && 
               !config.getApiKeys().isEmpty();
    }

    @Override
    public TtsResponse synthesize(TtsRequest request) {
        if (!isAvailable()) {
            return TtsResponse.builder()
                    .success(false)
                    .errorMessage("Provider not available: " + getProviderName())
                    .errorCode("PROVIDER_UNAVAILABLE")
                    .build();
        }

        long startTime = System.currentTimeMillis();
        
        try {
            log.info("Starting TTS synthesis with provider: {}", getProviderName());
            
            TtsResponse response = doSynthesize(request);
            
            long processingTime = System.currentTimeMillis() - startTime;
            response.setProcessingTime(processingTime);
            response.setProvider(getProviderName());
            
            log.info("TTS synthesis completed in {}ms", processingTime);
            
            return response;
            
        } catch (WebClientResponseException e) {
            log.error("TTS synthesis failed with HTTP error: {}", e.getMessage(), e);
            
            return TtsResponse.builder()
                    .success(false)
                    .errorMessage("HTTP error: " + e.getMessage())
                    .errorCode("HTTP_ERROR_" + e.getStatusCode().value())
                    .processingTime(System.currentTimeMillis() - startTime)
                    .provider(getProviderName())
                    .build();
                    
        } catch (Exception e) {
            log.error("TTS synthesis failed: {}", e.getMessage(), e);
            
            return TtsResponse.builder()
                    .success(false)
                    .errorMessage(e.getMessage())
                    .errorCode("SYNTHESIS_ERROR")
                    .processingTime(System.currentTimeMillis() - startTime)
                    .provider(getProviderName())
                    .build();
        }
    }

    /**
     * Abstract method for actual TTS synthesis implementation
     */
    protected abstract TtsResponse doSynthesize(TtsRequest request);

    /**
     * Get next API key using round-robin
     */
    protected String getNextApiKey() {
        List<String> apiKeys = config.getApiKeys();
        if (apiKeys == null || apiKeys.isEmpty()) {
            throw new RuntimeException("No API keys configured for provider: " + getProviderName());
        }

        int index = apiKeyIndex.getAndIncrement() % apiKeys.size();
        return apiKeys.get(index);
    }

    /**
     * Get WebClient with timeout configuration
     */
    protected WebClient getConfiguredWebClient() {
        return webClient.mutate()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
    }

    /**
     * Get request timeout
     */
    protected Duration getTimeout() {
        return Duration.ofMillis(config.getTimeout());
    }

    /**
     * Get retry count
     */
    protected int getRetryCount() {
        return config.getRetryCount();
    }

    /**
     * Get base URL
     */
    protected String getBaseUrl() {
        return config.getBaseUrl();
    }

    /**
     * Validate request
     */
    protected void validateRequest(TtsRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("TTS request cannot be null");
        }
        
        if (request.getText() == null || request.getText().trim().isEmpty()) {
            throw new IllegalArgumentException("Text cannot be empty");
        }
        
        if (request.getVoice() == null || request.getVoice().trim().isEmpty()) {
            throw new IllegalArgumentException("Voice cannot be empty");
        }
        
        if (request.getText().length() > 5000) {
            throw new IllegalArgumentException("Text too long (max 5000 characters)");
        }
    }

    /**
     * Build error response
     */
    protected TtsResponse buildErrorResponse(String errorMessage, String errorCode) {
        return TtsResponse.builder()
                .success(false)
                .errorMessage(errorMessage)
                .errorCode(errorCode)
                .provider(getProviderName())
                .build();
    }

    /**
     * Build success response
     */
    protected TtsResponse buildSuccessResponse(String audioUrl, byte[] audioData, 
                                               Integer duration, Long fileSize, 
                                               String rawResponse) {
        return TtsResponse.builder()
                .success(true)
                .audioUrl(audioUrl)
                .audioData(audioData)
                .duration(duration)
                .fileSize(fileSize)
                .rawResponse(rawResponse)
                .provider(getProviderName())
                .build();
    }
}
