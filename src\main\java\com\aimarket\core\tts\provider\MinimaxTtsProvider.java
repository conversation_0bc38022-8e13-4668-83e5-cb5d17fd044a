package com.aimarket.core.tts.provider;

import com.aimarket.config.AiMarketProperties;
import com.aimarket.core.tts.AbstractTtsProvider;
import com.aimarket.core.tts.TtsProvider;
import com.aimarket.core.tts.dto.TtsRequest;
import com.aimarket.core.tts.dto.TtsResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Map;

/**
 * Minimax TTS Provider
 * 
 * Implementation of TTS provider for Minimax service.
 */
@Slf4j
@Component
public class MinimaxTtsProvider extends AbstractTtsProvider {

    private static final String PROVIDER_NAME = "minimax";
    private final ObjectMapper objectMapper;

    public MinimaxTtsProvider(WebClient webClient, 
                              AiMarketProperties aiMarketProperties,
                              ObjectMapper objectMapper) {
        super(webClient, aiMarketProperties.getTts().getProviders().get(PROVIDER_NAME));
        this.objectMapper = objectMapper;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    protected TtsResponse doSynthesize(TtsRequest request) {
        validateRequest(request);
        
        try {
            // Prepare request payload
            Map<String, Object> payload = Map.of(
                "text", request.getText(),
                "voice_id", request.getVoice(),
                "speed", request.getSpeed() != null ? request.getSpeed() : 1.0,
                "vol", request.getVolume() != null ? request.getVolume() : 100,
                "pitch", request.getPitch() != null ? request.getPitch() : 0,
                "audio_format", request.getFormat() != null ? request.getFormat() : "mp3",
                "bitrate", 128000
            );

            // Make API call
            String response = getConfiguredWebClient()
                    .post()
                    .uri(getBaseUrl() + "/v1/text_to_speech")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + getNextApiKey())
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .bodyValue(payload)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(getTimeout())
                    .block();

            // Parse response
            JsonNode responseNode = objectMapper.readTree(response);
            
            if (responseNode.has("error")) {
                String errorMessage = responseNode.get("error").get("message").asText();
                String errorCode = responseNode.get("error").get("code").asText();
                return buildErrorResponse(errorMessage, errorCode);
            }

            // Extract audio information
            String audioUrl = responseNode.get("audio_url").asText();
            Integer duration = responseNode.has("duration") ? responseNode.get("duration").asInt() : null;
            Long fileSize = responseNode.has("file_size") ? responseNode.get("file_size").asLong() : null;

            return buildSuccessResponse(audioUrl, null, duration, fileSize, response);

        } catch (Exception e) {
            log.error("Minimax TTS synthesis failed", e);
            return buildErrorResponse(e.getMessage(), "MINIMAX_ERROR");
        }
    }

    @Override
    public List<Voice> getAvailableVoices() {
        return List.of(
            new Voice("female-1", "Female Voice 1", "zh-CN", "female", "Standard female voice", true),
            new Voice("male-1", "Male Voice 1", "zh-CN", "male", "Standard male voice", false),
            new Voice("female-2", "Female Voice 2", "zh-CN", "female", "Sweet female voice", false),
            new Voice("male-2", "Male Voice 2", "zh-CN", "male", "Deep male voice", false),
            new Voice("child-1", "Child Voice", "zh-CN", "child", "Child voice", false)
        );
    }

    @Override
    public List<String> getSupportedLanguages() {
        return List.of("zh-CN", "en-US", "ja-JP", "ko-KR");
    }

    @Override
    public List<String> getSupportedFormats() {
        return List.of("mp3", "wav", "flac");
    }
}
