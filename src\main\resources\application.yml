# AI Market Application Configuration
server:
  port: 8080
  servlet:
    context-path: /ai/market

spring:
  application:
    name: ai-market-java
  
  profiles:
    active: dev
  
  # Database Configuration
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # Redis Configuration
  data:
    redis:
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # Cache Configuration
  cache:
    type: redis
    redis:
      time-to-live: 600000
      key-prefix: "ai-market:"
      use-key-prefix: true
  
  # Jackson Configuration
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # Security Configuration
  security:
    jwt:
      secret: ${JWT_SECRET:ai-market-secret-key-change-in-production}
      expiration: 86400000 # 24 hours

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when_authorized
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Logging Configuration
logging:
  level:
    com.aimarket: INFO
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{requestId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{requestId}] %logger{36} - %msg%n"
  file:
    name: logs/ai-market.log
    max-size: 100MB
    max-history: 30

# AI Market Specific Configuration
aimarket:
  # TTS Configuration
  tts:
    providers:
      minimax:
        enabled: true
        base-url: ${MINIMAX_BASE_URL:https://api.minimax.chat}
        api-keys: ${MINIMAX_API_KEYS:}
        timeout: 30000
        retry-count: 3
      moyin:
        enabled: true
        base-url: ${MOYIN_BASE_URL:https://api.moyin.com}
        api-keys: ${MOYIN_API_KEYS:}
        timeout: 30000
        retry-count: 3
    default-provider: minimax
    load-balance: round-robin
  
  # Image Processing Configuration
  image:
    max-file-size: 10MB
    allowed-formats: jpg,jpeg,png,gif,webp
    processing-timeout: 60000
  
  # CORS Configuration
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8080}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: ******************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
  
  data:
    redis:
      host: localhost
      port: 6379
      password: ${REDIS_PASSWORD:}
      database: 0
  
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update

logging:
  level:
    com.aimarket: DEBUG
    org.springframework.web: DEBUG

---
# Test Profile
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  
  h2:
    console:
      enabled: true
  
  cache:
    type: simple

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:ai_market_prod}?useSSL=true&serverTimezone=UTC
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD}
      database: ${REDIS_DATABASE:0}
  
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

logging:
  level:
    com.aimarket: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
  file:
    name: /var/log/ai-market/application.log
