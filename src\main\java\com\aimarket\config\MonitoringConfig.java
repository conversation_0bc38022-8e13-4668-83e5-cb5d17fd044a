package com.aimarket.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * Monitoring Configuration
 * 
 * Configures Prometheus metrics and monitoring for the application.
 */
@Configuration
@RequiredArgsConstructor
public class MonitoringConfig {

    private final MeterRegistry meterRegistry;

    @Bean
    public AiMarketMetrics aiMarketMetrics() {
        return new AiMarketMetrics(meterRegistry);
    }

    /**
     * Custom metrics for AI Market application
     */
    @Component
    @RequiredArgsConstructor
    public static class AiMarketMetrics {

        private final MeterRegistry meterRegistry;

        // TTS Metrics
        private final Counter ttsRequestsTotal = Counter.builder("aimarket_tts_requests_total")
                .description("Total number of TTS requests")
                .register(meterRegistry);

        private final Counter ttsRequestsSuccessful = Counter.builder("aimarket_tts_requests_successful")
                .description("Number of successful TTS requests")
                .register(meterRegistry);

        private final Counter ttsRequestsFailed = Counter.builder("aimarket_tts_requests_failed")
                .description("Number of failed TTS requests")
                .register(meterRegistry);

        private final Timer ttsProcessingTime = Timer.builder("aimarket_tts_processing_time")
                .description("TTS processing time")
                .register(meterRegistry);

        // Image Processing Metrics
        private final Counter imageRequestsTotal = Counter.builder("aimarket_image_requests_total")
                .description("Total number of image processing requests")
                .register(meterRegistry);

        private final Counter imageRequestsSuccessful = Counter.builder("aimarket_image_requests_successful")
                .description("Number of successful image processing requests")
                .register(meterRegistry);

        private final Counter imageRequestsFailed = Counter.builder("aimarket_image_requests_failed")
                .description("Number of failed image processing requests")
                .register(meterRegistry);

        private final Timer imageProcessingTime = Timer.builder("aimarket_image_processing_time")
                .description("Image processing time")
                .register(meterRegistry);

        // User Metrics
        private final Counter userRegistrations = Counter.builder("aimarket_user_registrations_total")
                .description("Total number of user registrations")
                .register(meterRegistry);

        private final Counter userLogins = Counter.builder("aimarket_user_logins_total")
                .description("Total number of user logins")
                .register(meterRegistry);

        // API Metrics
        private final Counter apiRequestsTotal = Counter.builder("aimarket_api_requests_total")
                .description("Total number of API requests")
                .tag("endpoint", "unknown")
                .register(meterRegistry);

        private final Timer apiResponseTime = Timer.builder("aimarket_api_response_time")
                .description("API response time")
                .register(meterRegistry);

        // Provider Metrics
        private final Counter providerRequestsTotal = Counter.builder("aimarket_provider_requests_total")
                .description("Total number of provider requests")
                .tag("provider", "unknown")
                .register(meterRegistry);

        private final Counter providerRequestsSuccessful = Counter.builder("aimarket_provider_requests_successful")
                .description("Number of successful provider requests")
                .tag("provider", "unknown")
                .register(meterRegistry);

        private final Counter providerRequestsFailed = Counter.builder("aimarket_provider_requests_failed")
                .description("Number of failed provider requests")
                .tag("provider", "unknown")
                .register(meterRegistry);

        // TTS Metrics Methods
        public void incrementTtsRequests() {
            ttsRequestsTotal.increment();
        }

        public void incrementTtsRequestsSuccessful() {
            ttsRequestsSuccessful.increment();
        }

        public void incrementTtsRequestsFailed() {
            ttsRequestsFailed.increment();
        }

        public Timer.Sample startTtsProcessingTimer() {
            return Timer.start(meterRegistry);
        }

        public void recordTtsProcessingTime(Timer.Sample sample) {
            sample.stop(ttsProcessingTime);
        }

        // Image Processing Metrics Methods
        public void incrementImageRequests() {
            imageRequestsTotal.increment();
        }

        public void incrementImageRequestsSuccessful() {
            imageRequestsSuccessful.increment();
        }

        public void incrementImageRequestsFailed() {
            imageRequestsFailed.increment();
        }

        public Timer.Sample startImageProcessingTimer() {
            return Timer.start(meterRegistry);
        }

        public void recordImageProcessingTime(Timer.Sample sample) {
            sample.stop(imageProcessingTime);
        }

        // User Metrics Methods
        public void incrementUserRegistrations() {
            userRegistrations.increment();
        }

        public void incrementUserLogins() {
            userLogins.increment();
        }

        // API Metrics Methods
        public void incrementApiRequests(String endpoint) {
            Counter.builder("aimarket_api_requests_total")
                    .description("Total number of API requests")
                    .tag("endpoint", endpoint)
                    .register(meterRegistry)
                    .increment();
        }

        public Timer.Sample startApiResponseTimer() {
            return Timer.start(meterRegistry);
        }

        public void recordApiResponseTime(Timer.Sample sample) {
            sample.stop(apiResponseTime);
        }

        // Provider Metrics Methods
        public void incrementProviderRequests(String provider) {
            Counter.builder("aimarket_provider_requests_total")
                    .description("Total number of provider requests")
                    .tag("provider", provider)
                    .register(meterRegistry)
                    .increment();
        }

        public void incrementProviderRequestsSuccessful(String provider) {
            Counter.builder("aimarket_provider_requests_successful")
                    .description("Number of successful provider requests")
                    .tag("provider", provider)
                    .register(meterRegistry)
                    .increment();
        }

        public void incrementProviderRequestsFailed(String provider) {
            Counter.builder("aimarket_provider_requests_failed")
                    .description("Number of failed provider requests")
                    .tag("provider", provider)
                    .register(meterRegistry)
                    .increment();
        }

        // Custom Gauges
        public void registerActiveUsersGauge(java.util.function.Supplier<Number> valueSupplier) {
            io.micrometer.core.instrument.Gauge.builder("aimarket_active_users")
                    .description("Number of active users")
                    .register(meterRegistry, valueSupplier);
        }

        public void registerPendingTasksGauge(java.util.function.Supplier<Number> valueSupplier) {
            io.micrometer.core.instrument.Gauge.builder("aimarket_pending_tasks")
                    .description("Number of pending tasks")
                    .register(meterRegistry, valueSupplier);
        }

        public void registerCacheHitRateGauge(java.util.function.Supplier<Number> valueSupplier) {
            io.micrometer.core.instrument.Gauge.builder("aimarket_cache_hit_rate")
                    .description("Cache hit rate")
                    .register(meterRegistry, valueSupplier);
        }
    }
}
