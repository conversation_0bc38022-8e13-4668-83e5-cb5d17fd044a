package com.aimarket.controller;

import com.aimarket.core.tts.TtsFactory;
import com.aimarket.core.tts.TtsProvider;
import com.aimarket.core.tts.dto.TtsRequest;
import com.aimarket.model.TtsTask;
import com.aimarket.model.User;
import com.aimarket.service.TtsService;
import com.aimarket.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Audio Controller
 * 
 * Handles TTS (Text-to-Speech) related API endpoints.
 * Corresponds to Flask's app/controllers/audio/ module.
 */
@Slf4j
@RestController
@RequestMapping("/audio")
@RequiredArgsConstructor
public class AudioController extends BaseController {

    private final TtsService ttsService;
    private final TtsFactory ttsFactory;
    private final UserService userService;

    /**
     * Text-to-Speech API
     * POST /audio/tts
     */
    @PostMapping("/tts")
    public ResponseEntity<ApiResponse<TtsTaskResponse>> synthesizeText(
            @Valid @RequestBody TtsRequest request,
            @RequestParam(required = false) Long userId) {
        
        try {
            log.info("Received TTS request: text length={}, voice={}, provider={}", 
                    request.getText().length(), request.getVoice(), request.getProvider());

            // Get user (for demo purposes, using optional userId or default user)
            User user = null;
            if (userId != null) {
                Optional<User> userOpt = userService.findById(userId);
                if (userOpt.isPresent()) {
                    user = userOpt.get();
                } else {
                    return error("User not found with id: " + userId);
                }
            }

            // Process TTS request
            TtsTask task = ttsService.processTtsRequest(request, user);
            
            TtsTaskResponse response = TtsTaskResponse.fromEntity(task);
            
            return success(response, "TTS request submitted successfully");
            
        } catch (Exception e) {
            log.error("TTS request failed", e);
            return error("TTS request failed: " + e.getMessage());
        }
    }

    /**
     * Get TTS task status
     * GET /audio/tts/{taskId}
     */
    @GetMapping("/tts/{taskId}")
    public ResponseEntity<ApiResponse<TtsTaskResponse>> getTtsTask(@PathVariable Long taskId) {
        try {
            Optional<TtsTask> taskOpt = ttsService.findById(taskId);
            if (taskOpt.isEmpty()) {
                return notFound("TTS task not found with id: " + taskId);
            }
            
            TtsTaskResponse response = TtsTaskResponse.fromEntity(taskOpt.get());
            return success(response);
            
        } catch (Exception e) {
            log.error("Failed to get TTS task", e);
            return error("Failed to get TTS task: " + e.getMessage());
        }
    }

    /**
     * Get TTS task by request ID
     * GET /audio/tts/request/{requestId}
     */
    @GetMapping("/tts/request/{requestId}")
    public ResponseEntity<ApiResponse<TtsTaskResponse>> getTtsTaskByRequestId(@PathVariable String requestId) {
        try {
            Optional<TtsTask> taskOpt = ttsService.findByRequestId(requestId);
            if (taskOpt.isEmpty()) {
                return notFound("TTS task not found with request id: " + requestId);
            }
            
            TtsTaskResponse response = TtsTaskResponse.fromEntity(taskOpt.get());
            return success(response);
            
        } catch (Exception e) {
            log.error("Failed to get TTS task by request ID", e);
            return error("Failed to get TTS task: " + e.getMessage());
        }
    }

    /**
     * Get available voices
     * GET /audio/voices
     */
    @GetMapping("/voices")
    public ResponseEntity<ApiResponse<Map<String, List<TtsProvider.Voice>>>> getAvailableVoices(
            @RequestParam(required = false) String provider) {
        
        try {
            if (provider != null) {
                // Get voices for specific provider
                TtsProvider ttsProvider = ttsFactory.getProvider(provider);
                List<TtsProvider.Voice> voices = ttsProvider.getAvailableVoices();
                Map<String, List<TtsProvider.Voice>> result = Map.of(provider, voices);
                return success(result);
            } else {
                // Get voices for all providers
                Map<String, TtsProvider> providers = ttsFactory.getAllProviders();
                Map<String, List<TtsProvider.Voice>> allVoices = providers.entrySet().stream()
                        .collect(java.util.stream.Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().getAvailableVoices()
                        ));
                return success(allVoices);
            }
            
        } catch (Exception e) {
            log.error("Failed to get available voices", e);
            return error("Failed to get available voices: " + e.getMessage());
        }
    }

    /**
     * Get TTS tasks with pagination
     * GET /audio/tts
     */
    @GetMapping("/tts")
    public ResponseEntity<ApiResponse<Page<TtsTaskResponse>>> getTtsTasks(
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer size,
            @RequestParam(required = false) String sort,
            @RequestParam(required = false) String direction,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String provider,
            @RequestParam(required = false) Long userId) {
        
        try {
            Pageable pageable = createPageable(page, size, sort, direction);
            Page<TtsTask> tasks;
            
            if (status != null) {
                TtsTask.TaskStatus taskStatus = TtsTask.TaskStatus.valueOf(status.toUpperCase());
                tasks = ttsService.findByStatus(taskStatus, pageable);
            } else if (provider != null) {
                tasks = ttsService.findByProvider(provider, pageable);
            } else if (userId != null) {
                Optional<User> userOpt = userService.findById(userId);
                if (userOpt.isEmpty()) {
                    return error("User not found with id: " + userId);
                }
                tasks = ttsService.findByUser(userOpt.get(), pageable);
            } else {
                tasks = ttsService.findAll(pageable);
            }
            
            Page<TtsTaskResponse> response = tasks.map(TtsTaskResponse::fromEntity);
            return success(response);
            
        } catch (Exception e) {
            log.error("Failed to get TTS tasks", e);
            return error("Failed to get TTS tasks: " + e.getMessage());
        }
    }

    /**
     * Cancel TTS task
     * DELETE /audio/tts/{taskId}
     */
    @DeleteMapping("/tts/{taskId}")
    public ResponseEntity<ApiResponse<String>> cancelTtsTask(@PathVariable Long taskId) {
        try {
            ttsService.cancelTask(taskId);
            return success("Task cancelled successfully");
            
        } catch (Exception e) {
            log.error("Failed to cancel TTS task", e);
            return error("Failed to cancel TTS task: " + e.getMessage());
        }
    }

    /**
     * Test API
     * GET /audio/test
     */
    @GetMapping("/test")
    public ResponseEntity<ApiResponse<Map<String, Object>>> test() {
        Map<String, Object> testData = Map.of(
                "service", "Audio TTS Service",
                "status", "OK",
                "availableProviders", ttsFactory.getAvailableProviders(),
                "providerHealth", ttsFactory.getProviderHealthStatus()
        );
        
        return success(testData, "Audio service is running");
    }

    /**
     * TTS Task Response DTO
     */
    public static class TtsTaskResponse {
        private Long id;
        private String text;
        private String voice;
        private String provider;
        private String status;
        private String audioUrl;
        private Integer duration;
        private Long fileSize;
        private String errorMessage;
        private Integer processingTime;
        private String requestId;
        private String createdAt;
        private String updatedAt;

        public static TtsTaskResponse fromEntity(TtsTask task) {
            TtsTaskResponse response = new TtsTaskResponse();
            response.id = task.getId();
            response.text = task.getText();
            response.voice = task.getVoice();
            response.provider = task.getProvider();
            response.status = task.getStatus().name();
            response.audioUrl = task.getAudioUrl();
            response.duration = task.getDuration();
            response.fileSize = task.getFileSize();
            response.errorMessage = task.getErrorMessage();
            response.processingTime = task.getProcessingTime();
            response.requestId = task.getRequestId();
            response.createdAt = task.getCreatedAt() != null ? task.getCreatedAt().toString() : null;
            response.updatedAt = task.getUpdatedAt() != null ? task.getUpdatedAt().toString() : null;
            return response;
        }

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        
        public String getVoice() { return voice; }
        public void setVoice(String voice) { this.voice = voice; }
        
        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getAudioUrl() { return audioUrl; }
        public void setAudioUrl(String audioUrl) { this.audioUrl = audioUrl; }
        
        public Integer getDuration() { return duration; }
        public void setDuration(Integer duration) { this.duration = duration; }
        
        public Long getFileSize() { return fileSize; }
        public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public Integer getProcessingTime() { return processingTime; }
        public void setProcessingTime(Integer processingTime) { this.processingTime = processingTime; }
        
        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        
        public String getCreatedAt() { return createdAt; }
        public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
        
        public String getUpdatedAt() { return updatedAt; }
        public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }
    }
}
