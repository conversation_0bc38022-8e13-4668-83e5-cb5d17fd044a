package com.aimarket.repository;

import com.aimarket.model.ImageTask;
import com.aimarket.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Image Task Repository
 * 
 * Provides data access operations for ImageTask entities.
 */
@Repository
public interface ImageTaskRepository extends BaseRepository<ImageTask, Long> {

    /**
     * Find tasks by status
     */
    Page<ImageTask> findByStatus(ImageTask.TaskStatus status, Pageable pageable);

    /**
     * Find tasks by processing type
     */
    Page<ImageTask> findByProcessingType(ImageTask.ProcessingType processingType, Pageable pageable);

    /**
     * Find tasks by user
     */
    Page<ImageTask> findByUser(User user, Pageable pageable);

    /**
     * Find tasks by user and status
     */
    Page<ImageTask> findByUserAndStatus(User user, ImageTask.TaskStatus status, Pageable pageable);

    /**
     * Find task by request ID
     */
    Optional<ImageTask> findByRequestId(String requestId);

    /**
     * Find pending tasks
     */
    List<ImageTask> findByStatusOrderByCreatedAtAsc(ImageTask.TaskStatus status);

    /**
     * Find tasks created within time range
     */
    @Query("SELECT t FROM ImageTask t WHERE t.createdAt BETWEEN :startTime AND :endTime")
    Page<ImageTask> findTasksInTimeRange(@Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime, 
                                         Pageable pageable);

    /**
     * Count tasks by status
     */
    long countByStatus(ImageTask.TaskStatus status);

    /**
     * Count tasks by processing type
     */
    long countByProcessingType(ImageTask.ProcessingType processingType);

    /**
     * Count tasks by user
     */
    long countByUser(User user);

    /**
     * Find tasks by user and date range
     */
    @Query("SELECT t FROM ImageTask t WHERE t.user = :user AND t.createdAt BETWEEN :startDate AND :endDate")
    Page<ImageTask> findByUserAndDateRange(@Param("user") User user,
                                           @Param("startDate") LocalDateTime startDate,
                                           @Param("endDate") LocalDateTime endDate,
                                           Pageable pageable);

    /**
     * Get average processing time by type
     */
    @Query("SELECT AVG(t.processingTime) FROM ImageTask t WHERE t.processingType = :type AND t.status = 'COMPLETED'")
    Double getAverageProcessingTimeByType(@Param("type") ImageTask.ProcessingType type);

    /**
     * Get total processed size by user
     */
    @Query("SELECT SUM(t.processedSize) FROM ImageTask t WHERE t.user = :user AND t.status = 'COMPLETED'")
    Long getTotalProcessedSizeByUser(@Param("user") User user);

    /**
     * Find failed tasks for retry
     */
    @Query("SELECT t FROM ImageTask t WHERE t.status = 'FAILED' AND t.createdAt > :cutoffTime")
    List<ImageTask> findFailedTasksForRetry(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Find tasks by format
     */
    Page<ImageTask> findByFormat(String format, Pageable pageable);

    /**
     * Get compression statistics
     */
    @Query("SELECT AVG(CAST(t.processedSize AS double) / CAST(t.originalSize AS double)) FROM ImageTask t " +
           "WHERE t.processingType = 'COMPRESS' AND t.status = 'COMPLETED' AND t.originalSize > 0")
    Double getAverageCompressionRatio();
}
