package com.aimarket.service;

import com.aimarket.repository.BaseRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Base Service Class
 * 
 * Provides common service operations for all entities.
 * 
 * @param <T> Entity type
 * @param <ID> ID type
 * @param <R> Repository type
 */
@RequiredArgsConstructor
public abstract class BaseService<T, ID, R extends BaseRepository<T, ID>> {

    protected final R repository;

    /**
     * Find all entities with pagination
     */
    public Page<T> findAll(Pageable pageable) {
        return repository.findAll(pageable);
    }

    /**
     * Find entity by ID
     */
    public Optional<T> findById(ID id) {
        return repository.findById(id);
    }

    /**
     * Get entity by ID or throw exception
     */
    public T getById(ID id) {
        return repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Entity not found with id: " + id));
    }

    /**
     * Check if entity exists by ID
     */
    public boolean existsById(ID id) {
        return repository.existsById(id);
    }

    /**
     * Count all entities
     */
    public long count() {
        return repository.count();
    }

    /**
     * Save entity
     */
    @Transactional
    public T save(T entity) {
        return repository.save(entity);
    }

    /**
     * Save all entities
     */
    @Transactional
    public List<T> saveAll(Iterable<T> entities) {
        return repository.saveAll(entities);
    }

    /**
     * Delete entity by ID
     */
    @Transactional
    public void deleteById(ID id) {
        repository.deleteById(id);
    }

    /**
     * Delete entity
     */
    @Transactional
    public void delete(T entity) {
        repository.delete(entity);
    }
}
