package com.aimarket.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * AI Market Configuration Properties
 * 
 * This class holds all the configuration properties for the AI Market application.
 * It corresponds to the Flask config.py functionality.
 */
@Data
@Component
@ConfigurationProperties(prefix = "aimarket")
public class AiMarketProperties {

    private TtsConfig tts = new TtsConfig();
    private ImageConfig image = new ImageConfig();
    private CorsConfig cors = new CorsConfig();

    @Data
    public static class TtsConfig {
        private Map<String, ProviderConfig> providers;
        private String defaultProvider = "minimax";
        private String loadBalance = "round-robin";

        @Data
        public static class ProviderConfig {
            private boolean enabled = true;
            private String baseUrl;
            private List<String> apiKeys;
            private int timeout = 30000;
            private int retryCount = 3;
        }
    }

    @Data
    public static class ImageConfig {
        private String maxFileSize = "10MB";
        private List<String> allowedFormats = List.of("jpg", "jpeg", "png", "gif", "webp");
        private int processingTimeout = 60000;
    }

    @Data
    public static class CorsConfig {
        private List<String> allowedOrigins;
        private List<String> allowedMethods = List.of("GET", "POST", "PUT", "DELETE", "OPTIONS");
        private List<String> allowedHeaders = List.of("*");
        private boolean allowCredentials = true;
        private long maxAge = 3600;
    }
}
