package com.aimarket.util;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Base64;

/**
 * Audio Utilities
 * 
 * Utility functions for audio processing and manipulation.
 * Corresponds to Flask's utils/audio.py functionality.
 */
@Slf4j
public class AudioUtils {

    /**
     * Validate audio format
     */
    public static boolean isValidAudioFormat(String format) {
        if (format == null) {
            return false;
        }
        
        String lowerFormat = format.toLowerCase();
        return lowerFormat.equals("mp3") || 
               lowerFormat.equals("wav") || 
               lowerFormat.equals("flac") || 
               lowerFormat.equals("aac") ||
               lowerFormat.equals("ogg");
    }

    /**
     * Get audio duration from byte array (simplified implementation)
     */
    public static Integer getAudioDuration(byte[] audioData, String format) {
        if (audioData == null || audioData.length == 0) {
            return null;
        }
        
        // This is a simplified implementation
        // In a real application, you would use audio processing libraries
        // to extract actual duration from audio data
        
        try {
            // Estimate duration based on file size and format
            // This is just a rough estimation for demo purposes
            double estimatedDuration = switch (format.toLowerCase()) {
                case "mp3" -> audioData.length / 16000.0; // ~128kbps
                case "wav" -> audioData.length / 176400.0; // ~1411kbps
                case "flac" -> audioData.length / 100000.0; // ~800kbps
                default -> audioData.length / 16000.0;
            };
            
            return Math.max(1, (int) Math.round(estimatedDuration));
            
        } catch (Exception e) {
            log.warn("Failed to estimate audio duration", e);
            return null;
        }
    }

    /**
     * Convert base64 string to byte array
     */
    public static byte[] base64ToBytes(String base64String) {
        if (base64String == null || base64String.trim().isEmpty()) {
            return null;
        }
        
        try {
            // Remove data URL prefix if present
            if (base64String.startsWith("data:")) {
                int commaIndex = base64String.indexOf(',');
                if (commaIndex > 0) {
                    base64String = base64String.substring(commaIndex + 1);
                }
            }
            
            return Base64.getDecoder().decode(base64String);
            
        } catch (IllegalArgumentException e) {
            log.error("Failed to decode base64 string", e);
            return null;
        }
    }

    /**
     * Convert byte array to base64 string
     */
    public static String bytesToBase64(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * Validate audio file size
     */
    public static boolean isValidAudioSize(long fileSize, long maxSizeBytes) {
        return fileSize > 0 && fileSize <= maxSizeBytes;
    }

    /**
     * Get audio format from content type
     */
    public static String getFormatFromContentType(String contentType) {
        if (contentType == null) {
            return null;
        }
        
        return switch (contentType.toLowerCase()) {
            case "audio/mpeg", "audio/mp3" -> "mp3";
            case "audio/wav", "audio/wave" -> "wav";
            case "audio/flac" -> "flac";
            case "audio/aac" -> "aac";
            case "audio/ogg" -> "ogg";
            default -> null;
        };
    }

    /**
     * Get content type from audio format
     */
    public static String getContentTypeFromFormat(String format) {
        if (format == null) {
            return "application/octet-stream";
        }
        
        return switch (format.toLowerCase()) {
            case "mp3" -> "audio/mpeg";
            case "wav" -> "audio/wav";
            case "flac" -> "audio/flac";
            case "aac" -> "audio/aac";
            case "ogg" -> "audio/ogg";
            default -> "application/octet-stream";
        };
    }

    /**
     * Validate audio data integrity (basic check)
     */
    public static boolean isValidAudioData(byte[] audioData, String format) {
        if (audioData == null || audioData.length == 0) {
            return false;
        }
        
        try {
            // Basic format validation based on file headers
            return switch (format.toLowerCase()) {
                case "mp3" -> isValidMp3Header(audioData);
                case "wav" -> isValidWavHeader(audioData);
                case "flac" -> isValidFlacHeader(audioData);
                default -> true; // Skip validation for other formats
            };
            
        } catch (Exception e) {
            log.warn("Failed to validate audio data", e);
            return false;
        }
    }

    /**
     * Check MP3 file header
     */
    private static boolean isValidMp3Header(byte[] data) {
        if (data.length < 3) {
            return false;
        }
        
        // Check for MP3 frame sync (0xFF 0xFB or 0xFF 0xFA)
        return (data[0] & 0xFF) == 0xFF && 
               ((data[1] & 0xF0) == 0xF0);
    }

    /**
     * Check WAV file header
     */
    private static boolean isValidWavHeader(byte[] data) {
        if (data.length < 12) {
            return false;
        }
        
        // Check for RIFF header
        return data[0] == 'R' && data[1] == 'I' && data[2] == 'F' && data[3] == 'F' &&
               data[8] == 'W' && data[9] == 'A' && data[10] == 'V' && data[11] == 'E';
    }

    /**
     * Check FLAC file header
     */
    private static boolean isValidFlacHeader(byte[] data) {
        if (data.length < 4) {
            return false;
        }
        
        // Check for FLAC signature
        return data[0] == 'f' && data[1] == 'L' && data[2] == 'a' && data[3] == 'C';
    }

    /**
     * Calculate audio bitrate (estimated)
     */
    public static Integer calculateBitrate(long fileSize, Integer duration) {
        if (fileSize <= 0 || duration == null || duration <= 0) {
            return null;
        }
        
        // Bitrate = (file size in bits) / (duration in seconds)
        long fileSizeInBits = fileSize * 8;
        return (int) (fileSizeInBits / duration);
    }

    /**
     * Format file size for display
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * Format duration for display
     */
    public static String formatDuration(Integer seconds) {
        if (seconds == null || seconds < 0) {
            return "Unknown";
        }
        
        if (seconds < 60) {
            return seconds + "s";
        } else if (seconds < 3600) {
            int minutes = seconds / 60;
            int remainingSeconds = seconds % 60;
            return String.format("%dm %ds", minutes, remainingSeconds);
        } else {
            int hours = seconds / 3600;
            int minutes = (seconds % 3600) / 60;
            int remainingSeconds = seconds % 60;
            return String.format("%dh %dm %ds", hours, minutes, remainingSeconds);
        }
    }
}
