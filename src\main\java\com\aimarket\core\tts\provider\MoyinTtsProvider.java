package com.aimarket.core.tts.provider;

import com.aimarket.config.AiMarketProperties;
import com.aimarket.core.tts.AbstractTtsProvider;
import com.aimarket.core.tts.TtsProvider;
import com.aimarket.core.tts.dto.TtsRequest;
import com.aimarket.core.tts.dto.TtsResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Map;

/**
 * Moyin TTS Provider
 * 
 * Implementation of TTS provider for Moyin service.
 */
@Slf4j
@Component
public class MoyinTtsProvider extends AbstractTtsProvider {

    private static final String PROVIDER_NAME = "moyin";
    private final ObjectMapper objectMapper;

    public MoyinTtsProvider(WebClient webClient, 
                            AiMarketProperties aiMarketProperties,
                            ObjectMapper objectMapper) {
        super(webClient, aiMarketProperties.getTts().getProviders().get(PROVIDER_NAME));
        this.objectMapper = objectMapper;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    protected TtsResponse doSynthesize(TtsRequest request) {
        validateRequest(request);
        
        try {
            // Prepare request payload for Moyin API
            Map<String, Object> payload = Map.of(
                "content", request.getText(),
                "speaker", request.getVoice(),
                "speed", request.getSpeed() != null ? request.getSpeed() : 1,
                "volume", request.getVolume() != null ? request.getVolume() : 100,
                "pitch", request.getPitch() != null ? request.getPitch() : 0,
                "format", request.getFormat() != null ? request.getFormat() : "mp3",
                "language", request.getLanguage() != null ? request.getLanguage() : "zh-CN"
            );

            // Add optional parameters
            if (request.getEmotion() != null) {
                payload = Map.of(
                    "content", request.getText(),
                    "speaker", request.getVoice(),
                    "speed", request.getSpeed() != null ? request.getSpeed() : 1,
                    "volume", request.getVolume() != null ? request.getVolume() : 100,
                    "pitch", request.getPitch() != null ? request.getPitch() : 0,
                    "format", request.getFormat() != null ? request.getFormat() : "mp3",
                    "language", request.getLanguage() != null ? request.getLanguage() : "zh-CN",
                    "emotion", request.getEmotion()
                );
            }

            // Make API call
            String response = getConfiguredWebClient()
                    .post()
                    .uri(getBaseUrl() + "/api/v1/tts")
                    .header("X-API-Key", getNextApiKey())
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .bodyValue(payload)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(getTimeout())
                    .block();

            // Parse response
            JsonNode responseNode = objectMapper.readTree(response);
            
            if (!responseNode.get("success").asBoolean()) {
                String errorMessage = responseNode.get("message").asText();
                String errorCode = responseNode.has("code") ? responseNode.get("code").asText() : "MOYIN_ERROR";
                return buildErrorResponse(errorMessage, errorCode);
            }

            // Extract audio information
            JsonNode dataNode = responseNode.get("data");
            String audioUrl = dataNode.get("audio_url").asText();
            Integer duration = dataNode.has("duration") ? dataNode.get("duration").asInt() : null;
            Long fileSize = dataNode.has("file_size") ? dataNode.get("file_size").asLong() : null;

            return buildSuccessResponse(audioUrl, null, duration, fileSize, response);

        } catch (Exception e) {
            log.error("Moyin TTS synthesis failed", e);
            return buildErrorResponse(e.getMessage(), "MOYIN_ERROR");
        }
    }

    @Override
    public List<Voice> getAvailableVoices() {
        return List.of(
            new Voice("xiaoxiao", "小小", "zh-CN", "female", "温柔女声", true),
            new Voice("xiaogang", "小刚", "zh-CN", "male", "磁性男声", false),
            new Voice("xiaomei", "小美", "zh-CN", "female", "甜美女声", false),
            new Voice("xiaolei", "小雷", "zh-CN", "male", "浑厚男声", false),
            new Voice("xiaoyu", "小雨", "zh-CN", "female", "清新女声", false),
            new Voice("xiaofeng", "小风", "zh-CN", "male", "年轻男声", false)
        );
    }

    @Override
    public List<String> getSupportedLanguages() {
        return List.of("zh-CN", "zh-TW", "en-US", "ja-JP");
    }

    @Override
    public List<String> getSupportedFormats() {
        return List.of("mp3", "wav", "pcm");
    }
}
