package com.aimarket.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Cache Service
 * 
 * Provides caching functionality using Redis.
 * Corresponds to Flask's caching functionality.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;

    private static final String CACHE_PREFIX = "ai-market:";
    private static final Duration DEFAULT_TTL = Duration.ofMinutes(10);

    /**
     * Store value in cache with default TTL
     */
    public void put(String key, Object value) {
        put(key, value, DEFAULT_TTL);
    }

    /**
     * Store value in cache with custom TTL
     */
    public void put(String key, Object value, Duration ttl) {
        try {
            String fullKey = CACHE_PREFIX + key;
            redisTemplate.opsForValue().set(fullKey, value, ttl.toSeconds(), TimeUnit.SECONDS);
            log.debug("Cached value for key: {} with TTL: {}", fullKey, ttl);
        } catch (Exception e) {
            log.error("Failed to cache value for key: {}", key, e);
        }
    }

    /**
     * Get value from cache
     */
    public <T> T get(String key, Class<T> type) {
        try {
            String fullKey = CACHE_PREFIX + key;
            Object value = redisTemplate.opsForValue().get(fullKey);
            
            if (value == null) {
                log.debug("Cache miss for key: {}", fullKey);
                return null;
            }
            
            log.debug("Cache hit for key: {}", fullKey);
            
            if (type.isInstance(value)) {
                return type.cast(value);
            } else if (value instanceof String && !type.equals(String.class)) {
                // Try to deserialize JSON string
                return objectMapper.readValue((String) value, type);
            } else {
                // Try to convert using ObjectMapper
                return objectMapper.convertValue(value, type);
            }
            
        } catch (Exception e) {
            log.error("Failed to get cached value for key: {}", key, e);
            return null;
        }
    }

    /**
     * Get string value from cache
     */
    public String getString(String key) {
        return get(key, String.class);
    }

    /**
     * Check if key exists in cache
     */
    public boolean exists(String key) {
        try {
            String fullKey = CACHE_PREFIX + key;
            return Boolean.TRUE.equals(redisTemplate.hasKey(fullKey));
        } catch (Exception e) {
            log.error("Failed to check existence of key: {}", key, e);
            return false;
        }
    }

    /**
     * Delete value from cache
     */
    public void delete(String key) {
        try {
            String fullKey = CACHE_PREFIX + key;
            redisTemplate.delete(fullKey);
            log.debug("Deleted cached value for key: {}", fullKey);
        } catch (Exception e) {
            log.error("Failed to delete cached value for key: {}", key, e);
        }
    }

    /**
     * Delete multiple keys from cache
     */
    public void delete(String... keys) {
        for (String key : keys) {
            delete(key);
        }
    }

    /**
     * Delete keys matching pattern
     */
    public void deletePattern(String pattern) {
        try {
            String fullPattern = CACHE_PREFIX + pattern;
            Set<String> keys = redisTemplate.keys(fullPattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.debug("Deleted {} cached values matching pattern: {}", keys.size(), fullPattern);
            }
        } catch (Exception e) {
            log.error("Failed to delete cached values matching pattern: {}", pattern, e);
        }
    }

    /**
     * Set expiration time for existing key
     */
    public void expire(String key, Duration ttl) {
        try {
            String fullKey = CACHE_PREFIX + key;
            redisTemplate.expire(fullKey, ttl.toSeconds(), TimeUnit.SECONDS);
            log.debug("Set expiration for key: {} to {}", fullKey, ttl);
        } catch (Exception e) {
            log.error("Failed to set expiration for key: {}", key, e);
        }
    }

    /**
     * Get remaining TTL for key
     */
    public Duration getTtl(String key) {
        try {
            String fullKey = CACHE_PREFIX + key;
            Long ttl = redisTemplate.getExpire(fullKey, TimeUnit.SECONDS);
            return ttl != null && ttl > 0 ? Duration.ofSeconds(ttl) : Duration.ZERO;
        } catch (Exception e) {
            log.error("Failed to get TTL for key: {}", key, e);
            return Duration.ZERO;
        }
    }

    /**
     * Increment numeric value in cache
     */
    public Long increment(String key) {
        return increment(key, 1L);
    }

    /**
     * Increment numeric value in cache by delta
     */
    public Long increment(String key, long delta) {
        try {
            String fullKey = CACHE_PREFIX + key;
            return redisTemplate.opsForValue().increment(fullKey, delta);
        } catch (Exception e) {
            log.error("Failed to increment value for key: {}", key, e);
            return null;
        }
    }

    /**
     * Decrement numeric value in cache
     */
    public Long decrement(String key) {
        return decrement(key, 1L);
    }

    /**
     * Decrement numeric value in cache by delta
     */
    public Long decrement(String key, long delta) {
        try {
            String fullKey = CACHE_PREFIX + key;
            return redisTemplate.opsForValue().decrement(fullKey, delta);
        } catch (Exception e) {
            log.error("Failed to decrement value for key: {}", key, e);
            return null;
        }
    }

    /**
     * Add item to set
     */
    public void addToSet(String key, Object... values) {
        try {
            String fullKey = CACHE_PREFIX + key;
            redisTemplate.opsForSet().add(fullKey, values);
            log.debug("Added {} items to set: {}", values.length, fullKey);
        } catch (Exception e) {
            log.error("Failed to add items to set: {}", key, e);
        }
    }

    /**
     * Remove item from set
     */
    public void removeFromSet(String key, Object... values) {
        try {
            String fullKey = CACHE_PREFIX + key;
            redisTemplate.opsForSet().remove(fullKey, values);
            log.debug("Removed {} items from set: {}", values.length, fullKey);
        } catch (Exception e) {
            log.error("Failed to remove items from set: {}", key, e);
        }
    }

    /**
     * Check if item exists in set
     */
    public boolean isMemberOfSet(String key, Object value) {
        try {
            String fullKey = CACHE_PREFIX + key;
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(fullKey, value));
        } catch (Exception e) {
            log.error("Failed to check set membership for key: {}", key, e);
            return false;
        }
    }

    /**
     * Get all members of set
     */
    public Set<Object> getSetMembers(String key) {
        try {
            String fullKey = CACHE_PREFIX + key;
            return redisTemplate.opsForSet().members(fullKey);
        } catch (Exception e) {
            log.error("Failed to get set members for key: {}", key, e);
            return Set.of();
        }
    }

    /**
     * Clear all cache entries
     */
    public void clearAll() {
        try {
            Set<String> keys = redisTemplate.keys(CACHE_PREFIX + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("Cleared {} cached entries", keys.size());
            }
        } catch (Exception e) {
            log.error("Failed to clear all cache entries", e);
        }
    }

    /**
     * Get cache statistics
     */
    public CacheStats getStats() {
        try {
            Set<String> keys = redisTemplate.keys(CACHE_PREFIX + "*");
            long totalKeys = keys != null ? keys.size() : 0;
            
            // This is a simplified implementation
            // In a real application, you would track hit/miss ratios
            return new CacheStats(totalKeys, 0, 0, 0.0);
            
        } catch (Exception e) {
            log.error("Failed to get cache statistics", e);
            return new CacheStats(0, 0, 0, 0.0);
        }
    }

    /**
     * Cache Statistics
     */
    public record CacheStats(long totalKeys, long hits, long misses, double hitRate) {}

    // Spring Cache Annotations Support

    /**
     * Cache TTS voices
     */
    @Cacheable(value = "tts-voices", key = "#provider")
    public Object getTtsVoices(String provider) {
        // This method would be implemented by the calling service
        return null;
    }

    /**
     * Cache user data
     */
    @Cacheable(value = "users", key = "#userId")
    public Object getUser(Long userId) {
        // This method would be implemented by the calling service
        return null;
    }

    /**
     * Update cached user data
     */
    @CachePut(value = "users", key = "#userId")
    public Object updateUser(Long userId, Object user) {
        // This method would be implemented by the calling service
        return user;
    }

    /**
     * Evict user from cache
     */
    @CacheEvict(value = "users", key = "#userId")
    public void evictUser(Long userId) {
        log.debug("Evicted user from cache: {}", userId);
    }

    /**
     * Evict all users from cache
     */
    @CacheEvict(value = "users", allEntries = true)
    public void evictAllUsers() {
        log.debug("Evicted all users from cache");
    }
}
