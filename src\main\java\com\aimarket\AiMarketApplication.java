package com.aimarket;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * AI Market Application Main Class
 * 
 * This is the main entry point for the AI Market service platform.
 * It provides TTS (Text-to-Speech) and image processing capabilities
 * through RESTful APIs.
 * 
 * <AUTHOR> Market Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableTransactionManagement
public class AiMarketApplication {

    public static void main(String[] args) {
        SpringApplication.run(AiMarketApplication.class, args);
    }
}
