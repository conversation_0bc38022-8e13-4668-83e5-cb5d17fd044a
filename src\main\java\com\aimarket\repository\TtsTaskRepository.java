package com.aimarket.repository;

import com.aimarket.model.TtsTask;
import com.aimarket.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * TTS Task Repository
 * 
 * Provides data access operations for TtsTask entities.
 */
@Repository
public interface TtsTaskRepository extends BaseRepository<TtsTask, Long> {

    /**
     * Find tasks by status
     */
    Page<TtsTask> findByStatus(TtsTask.TaskStatus status, Pageable pageable);

    /**
     * Find tasks by provider
     */
    Page<TtsTask> findByProvider(String provider, Pageable pageable);

    /**
     * Find tasks by user
     */
    Page<TtsTask> findByUser(User user, Pageable pageable);

    /**
     * Find tasks by user and status
     */
    Page<TtsTask> findByUserAndStatus(User user, TtsTask.TaskStatus status, Pageable pageable);

    /**
     * Find task by request ID
     */
    Optional<TtsTask> findByRequestId(String requestId);

    /**
     * Find pending tasks
     */
    List<TtsTask> findByStatusOrderByCreatedAtAsc(TtsTask.TaskStatus status);

    /**
     * Find tasks created within time range
     */
    @Query("SELECT t FROM TtsTask t WHERE t.createdAt BETWEEN :startTime AND :endTime")
    Page<TtsTask> findTasksInTimeRange(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime, 
                                       Pageable pageable);

    /**
     * Count tasks by status
     */
    long countByStatus(TtsTask.TaskStatus status);

    /**
     * Count tasks by provider
     */
    long countByProvider(String provider);

    /**
     * Count tasks by user
     */
    long countByUser(User user);

    /**
     * Find tasks by user and date range
     */
    @Query("SELECT t FROM TtsTask t WHERE t.user = :user AND t.createdAt BETWEEN :startDate AND :endDate")
    Page<TtsTask> findByUserAndDateRange(@Param("user") User user,
                                         @Param("startDate") LocalDateTime startDate,
                                         @Param("endDate") LocalDateTime endDate,
                                         Pageable pageable);

    /**
     * Get average processing time by provider
     */
    @Query("SELECT AVG(t.processingTime) FROM TtsTask t WHERE t.provider = :provider AND t.status = 'COMPLETED'")
    Double getAverageProcessingTimeByProvider(@Param("provider") String provider);

    /**
     * Get total duration by user
     */
    @Query("SELECT SUM(t.duration) FROM TtsTask t WHERE t.user = :user AND t.status = 'COMPLETED'")
    Long getTotalDurationByUser(@Param("user") User user);

    /**
     * Find failed tasks for retry
     */
    @Query("SELECT t FROM TtsTask t WHERE t.status = 'FAILED' AND t.createdAt > :cutoffTime")
    List<TtsTask> findFailedTasksForRetry(@Param("cutoffTime") LocalDateTime cutoffTime);
}
