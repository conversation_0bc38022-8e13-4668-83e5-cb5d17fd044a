package com.aimarket.service;

import com.aimarket.model.User;
import com.aimarket.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * User Service
 * 
 * Provides business logic for user management operations.
 */
@Slf4j
@Service
public class UserService extends BaseService<User, Long, UserRepository> {

    private final PasswordEncoder passwordEncoder;

    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        super(userRepository);
        this.passwordEncoder = passwordEncoder;
    }

    /**
     * Find user by username
     */
    public Optional<User> findByUsername(String username) {
        return repository.findByUsername(username);
    }

    /**
     * Find user by email
     */
    public Optional<User> findByEmail(String email) {
        return repository.findByEmail(email);
    }

    /**
     * Find user by username or email
     */
    public Optional<User> findByUsernameOrEmail(String usernameOrEmail) {
        return repository.findByUsernameOrEmail(usernameOrEmail);
    }

    /**
     * Check if username exists
     */
    public boolean existsByUsername(String username) {
        return repository.existsByUsername(username);
    }

    /**
     * Check if email exists
     */
    public boolean existsByEmail(String email) {
        return repository.existsByEmail(email);
    }

    /**
     * Create new user
     */
    @Transactional
    public User createUser(User user) {
        log.info("Creating new user: {}", user.getUsername());
        
        // Check if username or email already exists
        if (existsByUsername(user.getUsername())) {
            throw new RuntimeException("Username already exists: " + user.getUsername());
        }
        
        if (existsByEmail(user.getEmail())) {
            throw new RuntimeException("Email already exists: " + user.getEmail());
        }
        
        // Encode password
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // Set default values
        if (user.getIsActive() == null) {
            user.setIsActive(true);
        }
        if (user.getIsVerified() == null) {
            user.setIsVerified(false);
        }
        if (user.getRole() == null) {
            user.setRole(User.UserRole.USER);
        }
        
        User savedUser = repository.save(user);
        log.info("User created successfully: {}", savedUser.getUsername());
        
        return savedUser;
    }

    /**
     * Update user
     */
    @Transactional
    public User updateUser(Long id, User userUpdate) {
        log.info("Updating user with id: {}", id);
        
        User existingUser = getById(id);
        
        // Update fields
        if (userUpdate.getEmail() != null && !userUpdate.getEmail().equals(existingUser.getEmail())) {
            if (existsByEmail(userUpdate.getEmail())) {
                throw new RuntimeException("Email already exists: " + userUpdate.getEmail());
            }
            existingUser.setEmail(userUpdate.getEmail());
        }
        
        if (userUpdate.getFullName() != null) {
            existingUser.setFullName(userUpdate.getFullName());
        }
        
        if (userUpdate.getPhone() != null) {
            existingUser.setPhone(userUpdate.getPhone());
        }
        
        if (userUpdate.getIsActive() != null) {
            existingUser.setIsActive(userUpdate.getIsActive());
        }
        
        if (userUpdate.getIsVerified() != null) {
            existingUser.setIsVerified(userUpdate.getIsVerified());
        }
        
        if (userUpdate.getRole() != null) {
            existingUser.setRole(userUpdate.getRole());
        }
        
        User savedUser = repository.save(existingUser);
        log.info("User updated successfully: {}", savedUser.getUsername());
        
        return savedUser;
    }

    /**
     * Change user password
     */
    @Transactional
    public void changePassword(Long id, String oldPassword, String newPassword) {
        log.info("Changing password for user id: {}", id);
        
        User user = getById(id);
        
        // Verify old password
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("Invalid old password");
        }
        
        // Update password
        user.setPassword(passwordEncoder.encode(newPassword));
        repository.save(user);
        
        log.info("Password changed successfully for user: {}", user.getUsername());
    }

    /**
     * Verify user
     */
    @Transactional
    public void verifyUser(Long id) {
        log.info("Verifying user with id: {}", id);
        
        User user = getById(id);
        user.setIsVerified(true);
        repository.save(user);
        
        log.info("User verified successfully: {}", user.getUsername());
    }

    /**
     * Deactivate user
     */
    @Transactional
    public void deactivateUser(Long id) {
        log.info("Deactivating user with id: {}", id);
        
        User user = getById(id);
        user.setIsActive(false);
        repository.save(user);
        
        log.info("User deactivated successfully: {}", user.getUsername());
    }

    /**
     * Find active users
     */
    public Page<User> findActiveUsers(Pageable pageable) {
        return repository.findByIsActiveTrue(pageable);
    }

    /**
     * Find users by role
     */
    public Page<User> findByRole(User.UserRole role, Pageable pageable) {
        return repository.findByRole(role, pageable);
    }

    /**
     * Search users
     */
    public Page<User> searchUsers(String keyword, Pageable pageable) {
        return repository.searchUsers(keyword, pageable);
    }

    /**
     * Get user statistics
     */
    public UserStats getUserStats() {
        long totalUsers = repository.count();
        long activeUsers = repository.countByIsActiveTrue();
        long verifiedUsers = repository.countByIsVerifiedTrue();
        
        return new UserStats(totalUsers, activeUsers, verifiedUsers);
    }

    /**
     * User Statistics DTO
     */
    public record UserStats(long totalUsers, long activeUsers, long verifiedUsers) {}
}
