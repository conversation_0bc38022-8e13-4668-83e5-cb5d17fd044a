package com.aimarket.service;

import com.aimarket.core.tts.TtsFactory;
import com.aimarket.core.tts.TtsProvider;
import com.aimarket.core.tts.dto.TtsRequest;
import com.aimarket.core.tts.dto.TtsResponse;
import com.aimarket.model.TtsTask;
import com.aimarket.model.User;
import com.aimarket.repository.TtsTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * TTS Service
 * 
 * Provides business logic for text-to-speech operations.
 */
@Slf4j
@Service
public class TtsService extends BaseService<TtsTask, Long, TtsTaskRepository> {

    private final TtsFactory ttsFactory;

    public TtsService(TtsTaskRepository ttsTaskRepository, TtsFactory ttsFactory) {
        super(ttsTaskRepository);
        this.ttsFactory = ttsFactory;
    }

    /**
     * Process TTS request
     */
    @Transactional
    public TtsTask processTtsRequest(TtsRequest request, User user) {
        log.info("Processing TTS request for user: {}", user.getUsername());
        
        // Create task
        TtsTask task = new TtsTask();
        task.setText(request.getText());
        task.setVoice(request.getVoice());
        task.setProvider(request.getProvider());
        task.setStatus(TtsTask.TaskStatus.PENDING);
        task.setUser(user);
        task.setRequestId(UUID.randomUUID().toString());
        
        // Save task
        task = repository.save(task);
        
        // Process asynchronously
        processTaskAsync(task);
        
        return task;
    }

    /**
     * Process task asynchronously
     */
    private void processTaskAsync(TtsTask task) {
        // This would typically be done in a separate thread or using @Async
        try {
            task.setStatus(TtsTask.TaskStatus.PROCESSING);
            repository.save(task);
            
            long startTime = System.currentTimeMillis();
            
            // Get TTS provider
            TtsProvider provider = ttsFactory.getProvider(task.getProvider());
            
            // Create TTS request
            TtsRequest request = TtsRequest.builder()
                    .text(task.getText())
                    .voice(task.getVoice())
                    .provider(task.getProvider())
                    .build();
            
            // Process TTS
            TtsResponse response = provider.synthesize(request);
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            // Update task
            task.setStatus(TtsTask.TaskStatus.COMPLETED);
            task.setAudioUrl(response.getAudioUrl());
            task.setDuration(response.getDuration());
            task.setFileSize(response.getFileSize());
            task.setProcessingTime((int) processingTime);
            task.setApiResponse(response.getRawResponse());
            
            repository.save(task);
            
            log.info("TTS task completed successfully: {}", task.getRequestId());
            
        } catch (Exception e) {
            log.error("TTS task failed: {}", task.getRequestId(), e);
            
            task.setStatus(TtsTask.TaskStatus.FAILED);
            task.setErrorMessage(e.getMessage());
            repository.save(task);
        }
    }

    /**
     * Get task by request ID
     */
    public Optional<TtsTask> findByRequestId(String requestId) {
        return repository.findByRequestId(requestId);
    }

    /**
     * Get tasks by user
     */
    public Page<TtsTask> findByUser(User user, Pageable pageable) {
        return repository.findByUser(user, pageable);
    }

    /**
     * Get tasks by status
     */
    public Page<TtsTask> findByStatus(TtsTask.TaskStatus status, Pageable pageable) {
        return repository.findByStatus(status, pageable);
    }

    /**
     * Get tasks by provider
     */
    public Page<TtsTask> findByProvider(String provider, Pageable pageable) {
        return repository.findByProvider(provider, pageable);
    }

    /**
     * Get pending tasks
     */
    public List<TtsTask> getPendingTasks() {
        return repository.findByStatusOrderByCreatedAtAsc(TtsTask.TaskStatus.PENDING);
    }

    /**
     * Cancel task
     */
    @Transactional
    public void cancelTask(Long taskId) {
        log.info("Cancelling TTS task: {}", taskId);
        
        TtsTask task = getById(taskId);
        
        if (task.getStatus() == TtsTask.TaskStatus.PENDING || 
            task.getStatus() == TtsTask.TaskStatus.PROCESSING) {
            task.setStatus(TtsTask.TaskStatus.CANCELLED);
            repository.save(task);
            
            log.info("TTS task cancelled: {}", taskId);
        } else {
            throw new RuntimeException("Cannot cancel task in status: " + task.getStatus());
        }
    }

    /**
     * Retry failed task
     */
    @Transactional
    public void retryTask(Long taskId) {
        log.info("Retrying TTS task: {}", taskId);
        
        TtsTask task = getById(taskId);
        
        if (task.getStatus() == TtsTask.TaskStatus.FAILED) {
            task.setStatus(TtsTask.TaskStatus.PENDING);
            task.setErrorMessage(null);
            repository.save(task);
            
            // Process again
            processTaskAsync(task);
            
            log.info("TTS task retry initiated: {}", taskId);
        } else {
            throw new RuntimeException("Cannot retry task in status: " + task.getStatus());
        }
    }

    /**
     * Get TTS statistics
     */
    public TtsStats getTtsStats() {
        long totalTasks = repository.count();
        long completedTasks = repository.countByStatus(TtsTask.TaskStatus.COMPLETED);
        long failedTasks = repository.countByStatus(TtsTask.TaskStatus.FAILED);
        long pendingTasks = repository.countByStatus(TtsTask.TaskStatus.PENDING);
        
        return new TtsStats(totalTasks, completedTasks, failedTasks, pendingTasks);
    }

    /**
     * Get provider statistics
     */
    public ProviderStats getProviderStats(String provider) {
        long totalTasks = repository.countByProvider(provider);
        Double avgProcessingTime = repository.getAverageProcessingTimeByProvider(provider);
        
        return new ProviderStats(provider, totalTasks, avgProcessingTime != null ? avgProcessingTime : 0.0);
    }

    /**
     * Get user TTS statistics
     */
    public UserTtsStats getUserTtsStats(User user) {
        long totalTasks = repository.countByUser(user);
        Long totalDuration = repository.getTotalDurationByUser(user);
        
        return new UserTtsStats(totalTasks, totalDuration != null ? totalDuration : 0L);
    }

    /**
     * TTS Statistics DTOs
     */
    public record TtsStats(long totalTasks, long completedTasks, long failedTasks, long pendingTasks) {}
    public record ProviderStats(String provider, long totalTasks, double avgProcessingTime) {}
    public record UserTtsStats(long totalTasks, long totalDuration) {}
}
