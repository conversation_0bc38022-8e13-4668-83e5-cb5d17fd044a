package com.aimarket.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TTS Task Entity
 * 
 * Represents a text-to-speech conversion task.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "tts_tasks", indexes = {
    @Index(name = "idx_tts_task_status", columnList = "status"),
    @Index(name = "idx_tts_task_provider", columnList = "provider"),
    @Index(name = "idx_tts_task_user_id", columnList = "user_id")
})
public class TtsTask extends BaseEntity {

    @NotBlank
    @Size(max = 5000)
    @Column(name = "text", nullable = false, length = 5000)
    private String text;

    @NotBlank
    @Size(max = 50)
    @Column(name = "voice", nullable = false, length = 50)
    private String voice;

    @NotBlank
    @Size(max = 20)
    @Column(name = "provider", nullable = false, length = 20)
    private String provider;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TaskStatus status = TaskStatus.PENDING;

    @Column(name = "audio_url", length = 500)
    private String audioUrl;

    @Column(name = "duration")
    private Integer duration; // in seconds

    @Column(name = "file_size")
    private Long fileSize; // in bytes

    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    @Column(name = "processing_time")
    private Integer processingTime; // in milliseconds

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Column(name = "request_id", length = 100)
    private String requestId;

    @Column(name = "api_response", columnDefinition = "TEXT")
    private String apiResponse;

    public enum TaskStatus {
        PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED
    }
}
