package com.aimarket.middleware;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.UUID;

/**
 * Request ID Interceptor
 * 
 * Generates and manages request IDs for tracking requests across the application.
 * Corresponds to Flask's request ID middleware functionality.
 */
@Slf4j
@Component
public class RequestIdInterceptor implements HandlerInterceptor {

    private static final String REQUEST_ID_HEADER = "X-Request-ID";
    private static final String REQUEST_ID_MDC_KEY = "requestId";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // Get request ID from header or generate new one
        String requestId = request.getHeader(REQUEST_ID_HEADER);
        if (requestId == null || requestId.trim().isEmpty()) {
            requestId = UUID.randomUUID().toString();
        }

        // Set request ID in MDC for logging
        MDC.put(REQUEST_ID_MDC_KEY, requestId);

        // Set request ID in response header
        response.setHeader(REQUEST_ID_HEADER, requestId);

        // Store request ID in request attributes for controllers to access
        request.setAttribute(REQUEST_ID_MDC_KEY, requestId);

        log.debug("Request started: {} {} [{}]", request.getMethod(), request.getRequestURI(), requestId);

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) {
        String requestId = (String) request.getAttribute(REQUEST_ID_MDC_KEY);
        
        log.debug("Request completed: {} {} [{}] - Status: {}", 
                request.getMethod(), request.getRequestURI(), requestId, response.getStatus());

        // Clear MDC to prevent memory leaks
        MDC.clear();
    }

    /**
     * Get current request ID from MDC
     */
    public static String getCurrentRequestId() {
        return MDC.get(REQUEST_ID_MDC_KEY);
    }
}
