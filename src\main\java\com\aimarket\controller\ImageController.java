package com.aimarket.controller;

import com.aimarket.model.ImageTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * Image Controller
 * 
 * Handles image processing related API endpoints.
 * Corresponds to Flask's app/controllers/image/ module.
 */
@Slf4j
@RestController
@RequestMapping("/v1/image")
@RequiredArgsConstructor
public class ImageController extends BaseController {

    /**
     * Image processing API
     * POST /v1/image/image
     */
    @PostMapping("/image")
    public ResponseEntity<ApiResponse<ImageTaskResponse>> processImage(
            @RequestParam("file") MultipartFile file,
            @RequestParam("type") String processingType,
            @RequestParam(value = "width", required = false) Integer width,
            @RequestParam(value = "height", required = false) Integer height,
            @RequestParam(value = "quality", required = false) Integer quality,
            @RequestParam(value = "format", required = false) String format,
            @RequestParam(value = "userId", required = false) Long userId) {
        
        try {
            log.info("Received image processing request: type={}, file size={}", 
                    processingType, file.getSize());

            // Validate file
            if (file.isEmpty()) {
                return error("File cannot be empty");
            }

            // Validate file size (10MB limit)
            if (file.getSize() > 10 * 1024 * 1024) {
                return error("File size cannot exceed 10MB");
            }

            // Validate file type
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return error("File must be an image");
            }

            // For demo purposes, create a mock response
            ImageTaskResponse response = new ImageTaskResponse();
            response.setId(System.currentTimeMillis());
            response.setProcessingType(processingType);
            response.setStatus("COMPLETED");
            response.setOriginalSize(file.getSize());
            response.setProcessedSize(file.getSize() * 80 / 100); // Simulate compression
            response.setFormat(format != null ? format : "jpg");
            response.setProcessedUrl("https://example.com/processed/" + System.currentTimeMillis() + ".jpg");
            response.setProcessingTime(1500); // 1.5 seconds

            return success(response, "Image processed successfully");
            
        } catch (Exception e) {
            log.error("Image processing failed", e);
            return error("Image processing failed: " + e.getMessage());
        }
    }

    /**
     * Get image task status
     * GET /v1/image/task/{taskId}
     */
    @GetMapping("/task/{taskId}")
    public ResponseEntity<ApiResponse<ImageTaskResponse>> getImageTask(@PathVariable Long taskId) {
        try {
            // For demo purposes, create a mock response
            ImageTaskResponse response = new ImageTaskResponse();
            response.setId(taskId);
            response.setProcessingType("RESIZE");
            response.setStatus("COMPLETED");
            response.setOriginalSize(1024000L);
            response.setProcessedSize(512000L);
            response.setFormat("jpg");
            response.setProcessedUrl("https://example.com/processed/" + taskId + ".jpg");
            response.setProcessingTime(2000);

            return success(response);
            
        } catch (Exception e) {
            log.error("Failed to get image task", e);
            return error("Failed to get image task: " + e.getMessage());
        }
    }

    /**
     * Get supported processing types
     * GET /v1/image/types
     */
    @GetMapping("/types")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSupportedTypes() {
        Map<String, Object> types = Map.of(
                "RESIZE", "Resize image to specified dimensions",
                "CROP", "Crop image to specified area",
                "ROTATE", "Rotate image by specified angle",
                "FILTER", "Apply filters to image",
                "ENHANCE", "Enhance image quality",
                "COMPRESS", "Compress image to reduce file size"
        );
        
        return success(types, "Supported processing types");
    }

    /**
     * Test API
     * GET /v1/image/test
     */
    @GetMapping("/test")
    public ResponseEntity<ApiResponse<Map<String, Object>>> test() {
        Map<String, Object> testData = Map.of(
                "service", "Image Processing Service",
                "status", "OK",
                "supportedFormats", java.util.List.of("jpg", "jpeg", "png", "gif", "webp"),
                "maxFileSize", "10MB"
        );
        
        return success(testData, "Image service is running");
    }

    /**
     * Image Task Response DTO
     */
    public static class ImageTaskResponse {
        private Long id;
        private String originalUrl;
        private String processedUrl;
        private String processingType;
        private String status;
        private Integer originalWidth;
        private Integer originalHeight;
        private Integer processedWidth;
        private Integer processedHeight;
        private Long originalSize;
        private Long processedSize;
        private String format;
        private String errorMessage;
        private Integer processingTime;
        private String requestId;
        private String createdAt;
        private String updatedAt;

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getOriginalUrl() { return originalUrl; }
        public void setOriginalUrl(String originalUrl) { this.originalUrl = originalUrl; }
        
        public String getProcessedUrl() { return processedUrl; }
        public void setProcessedUrl(String processedUrl) { this.processedUrl = processedUrl; }
        
        public String getProcessingType() { return processingType; }
        public void setProcessingType(String processingType) { this.processingType = processingType; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public Integer getOriginalWidth() { return originalWidth; }
        public void setOriginalWidth(Integer originalWidth) { this.originalWidth = originalWidth; }
        
        public Integer getOriginalHeight() { return originalHeight; }
        public void setOriginalHeight(Integer originalHeight) { this.originalHeight = originalHeight; }
        
        public Integer getProcessedWidth() { return processedWidth; }
        public void setProcessedWidth(Integer processedWidth) { this.processedWidth = processedWidth; }
        
        public Integer getProcessedHeight() { return processedHeight; }
        public void setProcessedHeight(Integer processedHeight) { this.processedHeight = processedHeight; }
        
        public Long getOriginalSize() { return originalSize; }
        public void setOriginalSize(Long originalSize) { this.originalSize = originalSize; }
        
        public Long getProcessedSize() { return processedSize; }
        public void setProcessedSize(Long processedSize) { this.processedSize = processedSize; }
        
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public Integer getProcessingTime() { return processingTime; }
        public void setProcessingTime(Integer processingTime) { this.processingTime = processingTime; }
        
        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        
        public String getCreatedAt() { return createdAt; }
        public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
        
        public String getUpdatedAt() { return updatedAt; }
        public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }
    }
}
