package com.aimarket.controller;

import com.aimarket.model.User;
import com.aimarket.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Demo Controller
 * 
 * Provides demonstration and testing APIs.
 * Corresponds to Flask's app/controllers/demo/ module.
 */
@Slf4j
@RestController
@RequestMapping("/demo")
@RequiredArgsConstructor
public class DemoController extends BaseController {

    private final UserService userService;

    /**
     * Hello API
     * GET /demo/hello
     */
    @GetMapping("/hello")
    public ResponseEntity<ApiResponse<Map<String, Object>>> hello(
            @RequestParam(value = "name", defaultValue = "World") String name) {
        
        Map<String, Object> response = Map.of(
                "message", "Hello, " + name + "!",
                "service", "AI Market Java",
                "version", "1.0.0",
                "timestamp", java.time.LocalDateTime.now()
        );
        
        return success(response, "Hello from AI Market");
    }

    /**
     * Get all users
     * GET /demo/users
     */
    @GetMapping("/users")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsers(
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer size,
            @RequestParam(required = false) String sort,
            @RequestParam(required = false) String direction,
            @RequestParam(required = false) String search) {
        
        try {
            Pageable pageable = createPageable(page, size, sort, direction);
            Page<User> users;
            
            if (search != null && !search.trim().isEmpty()) {
                users = userService.searchUsers(search, pageable);
            } else {
                users = userService.findAll(pageable);
            }
            
            Page<UserResponse> response = users.map(UserResponse::fromEntity);
            return success(response);
            
        } catch (Exception e) {
            log.error("Failed to get users", e);
            return error("Failed to get users: " + e.getMessage());
        }
    }

    /**
     * Get user by ID
     * GET /demo/users/{id}
     */
    @GetMapping("/users/{id}")
    public ResponseEntity<ApiResponse<UserResponse>> getUser(@PathVariable Long id) {
        try {
            Optional<User> userOpt = userService.findById(id);
            if (userOpt.isEmpty()) {
                return notFound("User not found with id: " + id);
            }
            
            UserResponse response = UserResponse.fromEntity(userOpt.get());
            return success(response);
            
        } catch (Exception e) {
            log.error("Failed to get user", e);
            return error("Failed to get user: " + e.getMessage());
        }
    }

    /**
     * Create user
     * POST /demo/users
     */
    @PostMapping("/users")
    public ResponseEntity<ApiResponse<UserResponse>> createUser(@RequestBody CreateUserRequest request) {
        try {
            User user = new User();
            user.setUsername(request.getUsername());
            user.setEmail(request.getEmail());
            user.setPassword(request.getPassword());
            user.setFullName(request.getFullName());
            user.setPhone(request.getPhone());
            
            User createdUser = userService.createUser(user);
            UserResponse response = UserResponse.fromEntity(createdUser);
            
            return success(response, "User created successfully");
            
        } catch (Exception e) {
            log.error("Failed to create user", e);
            return error("Failed to create user: " + e.getMessage());
        }
    }

    /**
     * Update user
     * PUT /demo/users/{id}
     */
    @PutMapping("/users/{id}")
    public ResponseEntity<ApiResponse<UserResponse>> updateUser(
            @PathVariable Long id, 
            @RequestBody UpdateUserRequest request) {
        
        try {
            User userUpdate = new User();
            userUpdate.setEmail(request.getEmail());
            userUpdate.setFullName(request.getFullName());
            userUpdate.setPhone(request.getPhone());
            userUpdate.setIsActive(request.getIsActive());
            userUpdate.setIsVerified(request.getIsVerified());
            
            User updatedUser = userService.updateUser(id, userUpdate);
            UserResponse response = UserResponse.fromEntity(updatedUser);
            
            return success(response, "User updated successfully");
            
        } catch (Exception e) {
            log.error("Failed to update user", e);
            return error("Failed to update user: " + e.getMessage());
        }
    }

    /**
     * Delete user
     * DELETE /demo/users/{id}
     */
    @DeleteMapping("/users/{id}")
    public ResponseEntity<ApiResponse<String>> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteById(id);
            return success("User deleted successfully");
            
        } catch (Exception e) {
            log.error("Failed to delete user", e);
            return error("Failed to delete user: " + e.getMessage());
        }
    }

    /**
     * Get products (demo data)
     * GET /demo/products
     */
    @GetMapping("/products")
    public ResponseEntity<ApiResponse<List<ProductResponse>>> getProducts() {
        List<ProductResponse> products = List.of(
                new ProductResponse(1L, "TTS Service", "Text-to-Speech conversion", 0.01, "ACTIVE"),
                new ProductResponse(2L, "Image Processing", "Image manipulation and enhancement", 0.05, "ACTIVE"),
                new ProductResponse(3L, "Voice Cloning", "Custom voice generation", 0.10, "BETA"),
                new ProductResponse(4L, "Audio Enhancement", "Audio quality improvement", 0.03, "ACTIVE")
        );
        
        return success(products, "Products retrieved successfully");
    }

    /**
     * Get service statistics
     * GET /demo/stats
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStats() {
        try {
            UserService.UserStats userStats = userService.getUserStats();
            
            Map<String, Object> stats = Map.of(
                    "users", Map.of(
                            "total", userStats.totalUsers(),
                            "active", userStats.activeUsers(),
                            "verified", userStats.verifiedUsers()
                    ),
                    "system", Map.of(
                            "uptime", "24h 15m",
                            "version", "1.0.0",
                            "environment", "development"
                    )
            );
            
            return success(stats, "Statistics retrieved successfully");
            
        } catch (Exception e) {
            log.error("Failed to get statistics", e);
            return error("Failed to get statistics: " + e.getMessage());
        }
    }

    // DTOs
    public static class UserResponse {
        private Long id;
        private String username;
        private String email;
        private String fullName;
        private String phone;
        private Boolean isActive;
        private Boolean isVerified;
        private String role;
        private String createdAt;

        public static UserResponse fromEntity(User user) {
            UserResponse response = new UserResponse();
            response.id = user.getId();
            response.username = user.getUsername();
            response.email = user.getEmail();
            response.fullName = user.getFullName();
            response.phone = user.getPhone();
            response.isActive = user.getIsActive();
            response.isVerified = user.getIsVerified();
            response.role = user.getRole().name();
            response.createdAt = user.getCreatedAt() != null ? user.getCreatedAt().toString() : null;
            return response;
        }

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getFullName() { return fullName; }
        public void setFullName(String fullName) { this.fullName = fullName; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public Boolean getIsActive() { return isActive; }
        public void setIsActive(Boolean isActive) { this.isActive = isActive; }
        public Boolean getIsVerified() { return isVerified; }
        public void setIsVerified(Boolean isVerified) { this.isVerified = isVerified; }
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
        public String getCreatedAt() { return createdAt; }
        public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
    }

    public static class CreateUserRequest {
        private String username;
        private String email;
        private String password;
        private String fullName;
        private String phone;

        // Getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getFullName() { return fullName; }
        public void setFullName(String fullName) { this.fullName = fullName; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
    }

    public static class UpdateUserRequest {
        private String email;
        private String fullName;
        private String phone;
        private Boolean isActive;
        private Boolean isVerified;

        // Getters and setters
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getFullName() { return fullName; }
        public void setFullName(String fullName) { this.fullName = fullName; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public Boolean getIsActive() { return isActive; }
        public void setIsActive(Boolean isActive) { this.isActive = isActive; }
        public Boolean getIsVerified() { return isVerified; }
        public void setIsVerified(Boolean isVerified) { this.isVerified = isVerified; }
    }

    public record ProductResponse(Long id, String name, String description, Double price, String status) {}
}
