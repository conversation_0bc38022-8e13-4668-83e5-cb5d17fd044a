package com.aimarket.controller;

import com.aimarket.core.tts.TtsFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Health Controller
 * 
 * Provides health check endpoints for service monitoring.
 */
@RestController
@RequiredArgsConstructor
public class HealthController extends BaseController implements HealthIndicator {

    private final TtsFactory ttsFactory;

    /**
     * Simple health check endpoint
     * GET /health
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> health() {
        Map<String, Object> healthData = Map.of(
                "status", "UP",
                "service", "AI Market Java",
                "version", "1.0.0",
                "timestamp", LocalDateTime.now(),
                "uptime", getUptime(),
                "components", getComponentsHealth()
        );
        
        return success(healthData, "Service is healthy");
    }

    /**
     * Detailed health check for Spring Boot Actuator
     */
    @Override
    public Health health() {
        try {
            Map<String, Object> details = Map.of(
                    "service", "AI Market Java",
                    "version", "1.0.0",
                    "uptime", getUptime(),
                    "ttsProviders", ttsFactory.getProviderHealthStatus()
            );
            
            return Health.up().withDetails(details).build();
            
        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }

    /**
     * Get service uptime (simplified)
     */
    private String getUptime() {
        // This is a simplified implementation
        // In a real application, you would track the actual start time
        return "Running";
    }

    /**
     * Get health status of all components
     */
    private Map<String, Object> getComponentsHealth() {
        return Map.of(
                "database", "UP",
                "redis", "UP", 
                "ttsProviders", ttsFactory.getProviderHealthStatus()
        );
    }
}
