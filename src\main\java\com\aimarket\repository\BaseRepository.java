package com.aimarket.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.List;
import java.util.Optional;

/**
 * Base Repository Interface
 * 
 * Provides common repository operations for all entities.
 * 
 * @param <T> Entity type
 * @param <ID> ID type
 */
@NoRepositoryBean
public interface BaseRepository<T, ID> extends JpaRepository<T, ID>, JpaSpecificationExecutor<T> {

    /**
     * Find all entities with pagination
     */
    Page<T> findAll(Pageable pageable);

    /**
     * Find entity by ID
     */
    Optional<T> findById(ID id);

    /**
     * Check if entity exists by ID
     */
    boolean existsById(ID id);

    /**
     * Count all entities
     */
    long count();

    /**
     * Delete entity by ID
     */
    void deleteById(ID id);

    /**
     * Save entity
     */
    <S extends T> S save(S entity);

    /**
     * Save all entities
     */
    <S extends T> List<S> saveAll(Iterable<S> entities);
}
