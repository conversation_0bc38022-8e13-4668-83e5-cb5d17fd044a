package com.aimarket.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Image Task Entity
 * 
 * Represents an image processing task.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "image_tasks", indexes = {
    @Index(name = "idx_image_task_status", columnList = "status"),
    @Index(name = "idx_image_task_type", columnList = "processing_type"),
    @Index(name = "idx_image_task_user_id", columnList = "user_id")
})
public class ImageTask extends BaseEntity {

    @NotBlank
    @Size(max = 500)
    @Column(name = "original_url", nullable = false, length = 500)
    private String originalUrl;

    @Column(name = "processed_url", length = 500)
    private String processedUrl;

    @Enumerated(EnumType.STRING)
    @Column(name = "processing_type", nullable = false)
    private ProcessingType processingType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TaskStatus status = TaskStatus.PENDING;

    @Column(name = "original_width")
    private Integer originalWidth;

    @Column(name = "original_height")
    private Integer originalHeight;

    @Column(name = "processed_width")
    private Integer processedWidth;

    @Column(name = "processed_height")
    private Integer processedHeight;

    @Column(name = "original_size")
    private Long originalSize; // in bytes

    @Column(name = "processed_size")
    private Long processedSize; // in bytes

    @Column(name = "format", length = 10)
    private String format;

    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    @Column(name = "processing_time")
    private Integer processingTime; // in milliseconds

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Column(name = "request_id", length = 100)
    private String requestId;

    @Column(name = "processing_params", columnDefinition = "TEXT")
    private String processingParams;

    public enum ProcessingType {
        RESIZE, CROP, ROTATE, FILTER, ENHANCE, COMPRESS
    }

    public enum TaskStatus {
        PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED
    }
}
