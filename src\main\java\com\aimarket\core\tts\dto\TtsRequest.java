package com.aimarket.core.tts.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;

/**
 * TTS Request DTO
 * 
 * Represents a text-to-speech conversion request.
 */
@Data
@Builder
public class TtsRequest {

    @NotBlank(message = "Text cannot be blank")
    @Size(max = 5000, message = "Text cannot exceed 5000 characters")
    private String text;

    @NotBlank(message = "Voice cannot be blank")
    @Size(max = 50, message = "Voice name cannot exceed 50 characters")
    private String voice;

    @NotBlank(message = "Provider cannot be blank")
    @Size(max = 20, message = "Provider name cannot exceed 20 characters")
    private String provider;

    private String format = "mp3"; // Default format

    private Integer speed = 1; // Default speed

    private Integer pitch = 0; // Default pitch

    private Integer volume = 100; // Default volume (0-100)

    private String language = "zh-CN"; // Default language

    private String emotion; // Optional emotion parameter

    private String style; // Optional style parameter
}
