package com.aimarket.middleware;

import com.aimarket.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Global Exception Handler
 * 
 * Handles all exceptions globally and provides consistent error responses.
 * Corresponds to Flask's error handling middleware functionality.
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * Handle validation errors
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleValidationErrors(
            MethodArgumentNotValidException ex) {
        
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        String requestId = RequestIdInterceptor.getCurrentRequestId();
        log.warn("Validation error [{}]: {}", requestId, errors);

        Map<String, Object> metadata = Map.of("validationErrors", errors, "requestId", requestId);
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(
                "Validation failed", metadata);

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Handle bind exceptions
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleBindException(BindException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        String requestId = RequestIdInterceptor.getCurrentRequestId();
        log.warn("Bind error [{}]: {}", requestId, errors);

        Map<String, Object> metadata = Map.of("bindErrors", errors, "requestId", requestId);
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(
                "Binding failed", metadata);

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Handle constraint violation exceptions
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleConstraintViolation(
            ConstraintViolationException ex) {
        
        Map<String, String> errors = ex.getConstraintViolations().stream()
                .collect(Collectors.toMap(
                        violation -> violation.getPropertyPath().toString(),
                        ConstraintViolation::getMessage
                ));

        String requestId = RequestIdInterceptor.getCurrentRequestId();
        log.warn("Constraint violation [{}]: {}", requestId, errors);

        Map<String, Object> metadata = Map.of("constraintViolations", errors, "requestId", requestId);
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(
                "Constraint violation", metadata);

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Handle missing request parameters
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleMissingParams(
            MissingServletRequestParameterException ex) {
        
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        String message = String.format("Missing required parameter: %s", ex.getParameterName());
        
        log.warn("Missing parameter [{}]: {}", requestId, message);

        Map<String, Object> metadata = Map.of("missingParameter", ex.getParameterName(), "requestId", requestId);
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(message, metadata);

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Handle method argument type mismatch
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleTypeMismatch(
            MethodArgumentTypeMismatchException ex) {
        
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        String message = String.format("Invalid value for parameter '%s': %s", 
                ex.getName(), ex.getValue());
        
        log.warn("Type mismatch [{}]: {}", requestId, message);

        Map<String, Object> metadata = Map.of(
                "parameter", ex.getName(),
                "value", ex.getValue(),
                "expectedType", ex.getRequiredType() != null ? ex.getRequiredType().getSimpleName() : "unknown",
                "requestId", requestId
        );
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(message, metadata);

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Handle HTTP message not readable
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleHttpMessageNotReadable(
            HttpMessageNotReadableException ex) {
        
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        String message = "Invalid JSON format or malformed request body";
        
        log.warn("Message not readable [{}]: {}", requestId, ex.getMessage());

        Map<String, Object> metadata = Map.of("requestId", requestId);
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(message, metadata);

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Handle unsupported HTTP method
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleMethodNotSupported(
            HttpRequestMethodNotSupportedException ex) {
        
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        String message = String.format("HTTP method '%s' not supported", ex.getMethod());
        
        log.warn("Method not supported [{}]: {}", requestId, message);

        Map<String, Object> metadata = Map.of(
                "method", ex.getMethod(),
                "supportedMethods", ex.getSupportedMethods(),
                "requestId", requestId
        );
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(message, metadata);

        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(response);
    }

    /**
     * Handle unsupported media type
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleUnsupportedMediaType(
            HttpMediaTypeNotSupportedException ex) {
        
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        String message = String.format("Media type '%s' not supported", ex.getContentType());
        
        log.warn("Unsupported media type [{}]: {}", requestId, message);

        Map<String, Object> metadata = Map.of(
                "contentType", ex.getContentType(),
                "supportedMediaTypes", ex.getSupportedMediaTypes(),
                "requestId", requestId
        );
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(message, metadata);

        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body(response);
    }

    /**
     * Handle file upload size exceeded
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleMaxSizeException(
            MaxUploadSizeExceededException ex) {
        
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        String message = "File size exceeds maximum allowed size";
        
        log.warn("File size exceeded [{}]: {}", requestId, ex.getMessage());

        Map<String, Object> metadata = Map.of(
                "maxFileSize", ex.getMaxUploadSize(),
                "requestId", requestId
        );
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(message, metadata);

        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(response);
    }

    /**
     * Handle no handler found (404)
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleNoHandlerFound(
            NoHandlerFoundException ex) {
        
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        String message = String.format("No handler found for %s %s", ex.getHttpMethod(), ex.getRequestURL());
        
        log.warn("No handler found [{}]: {}", requestId, message);

        Map<String, Object> metadata = Map.of(
                "method", ex.getHttpMethod(),
                "url", ex.getRequestURL(),
                "requestId", requestId
        );
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(message, metadata);

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    /**
     * Handle illegal argument exceptions
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleIllegalArgument(
            IllegalArgumentException ex) {
        
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        log.warn("Illegal argument [{}]: {}", requestId, ex.getMessage());

        Map<String, Object> metadata = Map.of("requestId", requestId);
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(ex.getMessage(), metadata);

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Handle runtime exceptions
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleRuntimeException(RuntimeException ex) {
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        log.error("Runtime exception [{}]: {}", requestId, ex.getMessage(), ex);

        Map<String, Object> metadata = Map.of("requestId", requestId);
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(
                "An error occurred while processing your request", metadata);

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * Handle all other exceptions
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<BaseController.ApiResponse<Object>> handleGenericException(Exception ex) {
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        log.error("Unexpected exception [{}]: {}", requestId, ex.getMessage(), ex);

        Map<String, Object> metadata = Map.of("requestId", requestId);
        BaseController.ApiResponse<Object> response = BaseController.ApiResponse.error(
                "An unexpected error occurred", metadata);

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
