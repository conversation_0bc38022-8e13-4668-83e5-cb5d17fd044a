package com.aimarket.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * JPA Configuration
 * 
 * Enables JPA auditing and repository scanning.
 */
@Configuration
@EnableJpaAuditing
@EnableJpaRepositories(basePackages = "com.aimarket.repository")
public class JpaConfig {
}
