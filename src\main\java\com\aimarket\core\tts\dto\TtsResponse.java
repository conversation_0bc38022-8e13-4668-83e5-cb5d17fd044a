package com.aimarket.core.tts.dto;

import lombok.Builder;
import lombok.Data;

/**
 * TTS Response DTO
 * 
 * Represents the response from a text-to-speech conversion.
 */
@Data
@Builder
public class TtsResponse {

    private String audioUrl;
    
    private byte[] audioData;
    
    private String format;
    
    private Integer duration; // in seconds
    
    private Long fileSize; // in bytes
    
    private String provider;
    
    private String voice;
    
    private String requestId;
    
    private String rawResponse; // Original API response
    
    private boolean success;
    
    private String errorMessage;
    
    private String errorCode;
    
    private long processingTime; // in milliseconds
}
